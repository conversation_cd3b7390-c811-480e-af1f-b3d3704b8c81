<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" agent="Mozilla/5.0" version="24.7.17">
  <diagram name="智能网联汽车检查助手-完整流程" id="main-system-flow">
    <mxGraphModel dx="1422" dy="800" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="2336" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 开始节点 -->
        <mxCell id="start" value="启动应用" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="760" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 初始化流程 -->
        <mxCell id="login" value="用户登录验证" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="740" y="140" width="160" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="init" value="系统初始化检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="740" y="240" width="160" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="task-create" value="创建检查任务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="740" y="340" width="160" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="team-config" value="团队信息配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="740" y="440" width="160" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="vehicle-info" value="车辆信息录入" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="740" y="540" width="160" height="60" as="geometry" />
        </mxCell>
        
        <!-- 设备自检 -->
        <mxCell id="device-check" value="设备状态自检" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="740" y="640" width="160" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="device-ok" value="设备检查通过?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="720" y="740" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="device-error" value="设备异常处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="1000" y="750" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- 检查模块 -->
        <mxCell id="check-main" value="检查主界面" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="740" y="860" width="160" height="60" as="geometry" />
        </mxCell>
        
        <!-- 六大检查模块 -->
        <mxCell id="safety-module" value="安全准备模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#b85450;fontSize=11;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="80" y="980" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="appearance-module" value="车身外观模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="280" y="980" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="electrical-module" value="电气系统模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="480" y="980" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="sensor-module" value="传感器检查模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="680" y="980" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="tool-module" value="工具设备模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="880" y="980" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="system-module" value="系统功能模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="1080" y="980" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- 安全准备子项 -->
        <mxCell id="safety-1" value="安全帽检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="20" y="1100" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="safety-2" value="安全警示牌" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="120" y="1100" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="safety-3" value="车轮挡块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="20" y="1150" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="safety-4" value="灭火器检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="120" y="1150" width="80" height="30" as="geometry" />
        </mxCell>
        
        <!-- 车身外观子项 -->
        <mxCell id="appearance-1" value="环车外观检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="220" y="1100" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="appearance-2" value="车身损伤记录" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="320" y="1100" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="appearance-3" value="灯光系统检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="220" y="1150" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="appearance-4" value="轮胎胎压测量" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="320" y="1150" width="80" height="30" as="geometry" />
        </mxCell>
        
        <!-- 电气系统子项 -->
        <mxCell id="electrical-1" value="蓄电池电压测量" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="420" y="1100" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="electrical-2" value="绝缘测试" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="520" y="1100" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="electrical-3" value="制动液检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="420" y="1150" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="electrical-4" value="冷却液检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="520" y="1150" width="80" height="30" as="geometry" />
        </mxCell>
        
        <!-- 传感器子项 -->
        <mxCell id="sensor-1" value="摄像头状态检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="620" y="1100" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="sensor-2" value="雷达传感器检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="720" y="1100" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="sensor-3" value="超声波传感器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="620" y="1150" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="sensor-4" value="传感器标定确认" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="720" y="1150" width="80" height="30" as="geometry" />
        </mxCell>
        
        <!-- 工具设备子项 -->
        <mxCell id="tool-1" value="万用表自检" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="820" y="1100" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="tool-2" value="绝缘测试仪自检" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="920" y="1100" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="tool-3" value="CAN分析仪自检" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="820" y="1150" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="tool-4" value="网线测试仪自检" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="920" y="1150" width="80" height="30" as="geometry" />
        </mxCell>
        
        <!-- 系统功能子项 -->
        <mxCell id="system-1" value="车辆上电检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="1020" y="1100" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="system-2" value="计算平台启动" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="1120" y="1100" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="system-3" value="系统功能验证" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="1020" y="1150" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="system-4" value="通信测试" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="1120" y="1150" width="80" height="30" as="geometry" />
        </mxCell>
        
        <!-- 检查完成判断 -->
        <mxCell id="all-complete" value="所有模块完成?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="720" y="1230" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- 报告生成流程 -->
        <mxCell id="summary" value="检查结果汇总" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="740" y="1350" width="160" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="report" value="生成检查报告" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="740" y="1450" width="160" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="audit" value="报告审核导出" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="740" y="1550" width="160" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="archive" value="数据存档" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="740" y="1650" width="160" height="60" as="geometry" />
        </mxCell>
        
        <!-- 结束节点 -->
        <mxCell id="end" value="任务结束" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="760" y="1750" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 单项检查详细流程（右侧） -->
        <mxCell id="single-start" value="进入检查项" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12" vertex="1" parent="1">
          <mxGeometry x="1320" y="200" width="100" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="show-title" value="显示检查标题和要求" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="1300" y="280" width="140" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="show-guide" value="显示操作指引" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="1300" y="340" width="140" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="safety-critical" value="安全关键项?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="1320" y="400" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="safety-warning" value="安全警告确认" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="1480" y="410" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="execute-check" value="执行检查操作" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="1300" y="500" width="140" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="need-data" value="需要数据录入?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="1320" y="560" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="data-input" value="数据输入界面" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="1480" y="570" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="need-photo" value="需要拍照证据?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="1320" y="640" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="take-photo" value="启动拍照功能" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="1480" y="650" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="result-confirm" value="结果确认选择" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="1300" y="720" width="140" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="pass-fail" value="合格/不合格?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="1320" y="780" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="mark-pass" value="标记为完成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="1200" y="790" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="mark-fail" value="填写问题描述" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="1480" y="790" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="update-progress" value="更新进度状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="1300" y="870" width="140" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="next-item" value="自动跳转下一项" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12" vertex="1" parent="1">
          <mxGeometry x="1320" y="940" width="100" height="50" as="geometry" />
        </mxCell>
        
        <!-- 主流程连线 -->
        <mxCell id="edge1" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="start" target="login">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge2" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="login" target="init">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge3" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="init" target="task-create">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge4" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="task-create" target="team-config">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge5" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="team-config" target="vehicle-info">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge6" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="vehicle-info" target="device-check">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge7" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="device-check" target="device-ok">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge8" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="device-ok" target="device-error">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge9" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="device-ok" target="check-main">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge10" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="device-error" target="device-check">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1070" y="640" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- 检查模块连线 -->
        <mxCell id="edge11" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="check-main" target="safety-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge12" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="check-main" target="appearance-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge13" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="check-main" target="electrical-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge14" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="check-main" target="sensor-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge15" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="check-main" target="tool-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge16" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="check-main" target="system-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 汇总和报告连线 -->
        <mxCell id="edge17" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="all-complete" target="summary">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge18" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="summary" target="report">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge19" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="report" target="audit">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge20" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="audit" target="archive">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge21" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="archive" target="end">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 循环检查连线 -->
        <mxCell id="edge22" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="all-complete" target="check-main">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="400" as="sourcePoint" />
            <mxPoint x="650" y="350" as="targetPoint" />
            <Array as="points">
              <mxPoint x="620" y="1270" />
              <mxPoint x="620" y="890" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- 单项检查流程连线 -->
        <mxCell id="edge23" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="single-start" target="show-title">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="400" as="sourcePoint" />
            <mxPoint x="1450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge24" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="show-title" target="show-guide">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="400" as="sourcePoint" />
            <mxPoint x="1450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge25" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="show-guide" target="safety-critical">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="400" as="sourcePoint" />
            <mxPoint x="1450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge26" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="safety-critical" target="safety-warning">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="400" as="sourcePoint" />
            <mxPoint x="1450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge27" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="safety-critical" target="execute-check">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="400" as="sourcePoint" />
            <mxPoint x="1450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge28" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="safety-warning" target="execute-check">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="400" as="sourcePoint" />
            <mxPoint x="1450" y="350" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1530" y="500" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge29" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="execute-check" target="need-data">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="400" as="sourcePoint" />
            <mxPoint x="1450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge30" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="need-data" target="data-input">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="400" as="sourcePoint" />
            <mxPoint x="1450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge31" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="need-data" target="need-photo">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="400" as="sourcePoint" />
            <mxPoint x="1450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge32" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="data-input" target="need-photo">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="400" as="sourcePoint" />
            <mxPoint x="1450" y="350" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1530" y="640" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge33" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="need-photo" target="take-photo">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="400" as="sourcePoint" />
            <mxPoint x="1450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge34" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="need-photo" target="result-confirm">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="400" as="sourcePoint" />
            <mxPoint x="1450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge35" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="take-photo" target="result-confirm">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="400" as="sourcePoint" />
            <mxPoint x="1450" y="350" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1530" y="720" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge36" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="result-confirm" target="pass-fail">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="400" as="sourcePoint" />
            <mxPoint x="1450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge37" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="pass-fail" target="mark-pass">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="400" as="sourcePoint" />
            <mxPoint x="1450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge38" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="pass-fail" target="mark-fail">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="400" as="sourcePoint" />
            <mxPoint x="1450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge39" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="mark-pass" target="update-progress">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="400" as="sourcePoint" />
            <mxPoint x="1450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge40" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="mark-fail" target="update-progress">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="400" as="sourcePoint" />
            <mxPoint x="1450" y="350" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1530" y="870" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge41" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="update-progress" target="next-item">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="400" as="sourcePoint" />
            <mxPoint x="1450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 标签文字 -->
        <mxCell id="label1" value="是" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="870" y="780" width="20" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label2" value="否" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="940" y="760" width="20" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label3" value="是" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="870" y="1310" width="20" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label4" value="否" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="580" y="1240" width="20" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label5" value="是" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1440" y="420" width="20" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label6" value="否" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1380" y="470" width="20" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label7" value="是" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1440" y="580" width="20" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label8" value="否" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1380" y="630" width="20" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label9" value="是" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1440" y="660" width="20" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label10" value="否" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1380" y="730" width="20" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label11" value="合格" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1260" y="800" width="30" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label12" value="不合格" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1440" y="800" width="40" height="20" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
