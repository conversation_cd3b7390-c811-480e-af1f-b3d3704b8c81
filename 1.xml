<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" agent="Mozilla/5.0" version="24.7.17">
  <diagram name="智能网联汽车检查助手-PPT流程图" id="ppt-flow">
    <mxGraphModel dx="1200" dy="900" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="900" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 标题 -->
        <mxCell id="title" value="智能网联汽车检查助手流程图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="400" y="20" width="400" height="40" as="geometry" />
        </mxCell>

        <!-- 第一阶段：系统准备 -->
        <mxCell id="phase1-title" value="系统准备阶段" style="text;html=1;strokeColor=none;fillColor=#e1d5e7;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="50" y="80" width="120" height="30" as="geometry" />
        </mxCell>

        <mxCell id="start" value="启动应用" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="50" y="130" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="login" value="用户登录验证" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="50" y="200" width="120" height="40" as="geometry" />
        </mxCell>

        <mxCell id="init" value="系统初始化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="50" y="260" width="120" height="40" as="geometry" />
        </mxCell>

        <!-- 第二阶段：任务配置 -->
        <mxCell id="phase2-title" value="任务配置阶段" style="text;html=1;strokeColor=none;fillColor=#fff2cc;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="220" y="80" width="120" height="30" as="geometry" />
        </mxCell>

        <mxCell id="task-create" value="创建检查任务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="220" y="130" width="120" height="40" as="geometry" />
        </mxCell>

        <mxCell id="team-config" value="团队信息配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="220" y="190" width="120" height="40" as="geometry" />
        </mxCell>

        <mxCell id="vehicle-info" value="车辆信息录入" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="220" y="250" width="120" height="40" as="geometry" />
        </mxCell>

        <!-- 第三阶段：设备检查 -->
        <mxCell id="phase3-title" value="设备检查阶段" style="text;html=1;strokeColor=none;fillColor=#e1d5e7;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="390" y="80" width="120" height="30" as="geometry" />
        </mxCell>

        <mxCell id="device-check" value="设备状态自检" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="390" y="130" width="120" height="40" as="geometry" />
        </mxCell>

        <mxCell id="device-ok" value="设备检查\n通过?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="410" y="190" width="80" height="60" as="geometry" />
        </mxCell>

        <mxCell id="device-error" value="设备异常处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="390" y="270" width="120" height="40" as="geometry" />
        </mxCell>

        <!-- 第四阶段：检查执行 -->
        <mxCell id="phase4-title" value="检查执行阶段" style="text;html=1;strokeColor=none;fillColor=#d5e8d4;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="560" y="80" width="120" height="30" as="geometry" />
        </mxCell>

        <mxCell id="check-main" value="检查主界面" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="560" y="130" width="120" height="40" as="geometry" />
        </mxCell>

        <!-- 六大检查模块 -->
        <mxCell id="modules-title" value="六大检查模块" style="text;html=1;strokeColor=none;fillColor=#f0f0f0;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="50" y="350" width="630" height="30" as="geometry" />
        </mxCell>

        <mxCell id="safety-module" value="安全准备模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#b85450;fontSize=10;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="50" y="400" width="100" height="50" as="geometry" />
        </mxCell>

        <mxCell id="appearance-module" value="车身外观模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="160" y="400" width="100" height="50" as="geometry" />
        </mxCell>

        <mxCell id="electrical-module" value="电气系统模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="270" y="400" width="100" height="50" as="geometry" />
        </mxCell>

        <mxCell id="sensor-module" value="传感器检查模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="380" y="400" width="100" height="50" as="geometry" />
        </mxCell>

        <mxCell id="tool-module" value="工具设备模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="490" y="400" width="100" height="50" as="geometry" />
        </mxCell>

        <mxCell id="system-module" value="系统功能模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="600" y="400" width="100" height="50" as="geometry" />
        </mxCell>

        <!-- 检查项详细内容 -->
        <mxCell id="safety-items" value="• 安全帽检查&#xa;• 安全警示牌&#xa;• 车轮挡块&#xa;• 灭火器检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;fontSize=9;align=left;verticalAlign=top" vertex="1" parent="1">
          <mxGeometry x="50" y="470" width="100" height="60" as="geometry" />
        </mxCell>

        <mxCell id="appearance-items" value="• 环车外观检查&#xa;• 车身损伤记录&#xa;• 灯光系统检查&#xa;• 轮胎胎压测量" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;fontSize=9;align=left;verticalAlign=top" vertex="1" parent="1">
          <mxGeometry x="160" y="470" width="100" height="60" as="geometry" />
        </mxCell>

        <mxCell id="electrical-items" value="• 蓄电池电压测量&#xa;• 绝缘测试&#xa;• 制动液检查&#xa;• 冷却液检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;fontSize=9;align=left;verticalAlign=top" vertex="1" parent="1">
          <mxGeometry x="270" y="470" width="100" height="60" as="geometry" />
        </mxCell>

        <mxCell id="sensor-items" value="• 摄像头状态检查&#xa;• 雷达传感器检查&#xa;• 超声波传感器&#xa;• 传感器标定确认" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;fontSize=9;align=left;verticalAlign=top" vertex="1" parent="1">
          <mxGeometry x="380" y="470" width="100" height="60" as="geometry" />
        </mxCell>

        <mxCell id="tool-items" value="• 万用表自检&#xa;• 绝缘测试仪自检&#xa;• CAN分析仪自检&#xa;• 网线测试仪自检" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;fontSize=9;align=left;verticalAlign=top" vertex="1" parent="1">
          <mxGeometry x="490" y="470" width="100" height="60" as="geometry" />
        </mxCell>

        <mxCell id="system-items" value="• 车辆上电检查&#xa;• 计算平台启动&#xa;• 系统功能验证&#xa;• 通信测试" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;fontSize=9;align=left;verticalAlign=top" vertex="1" parent="1">
          <mxGeometry x="600" y="470" width="100" height="60" as="geometry" />
        </mxCell>

        <!-- 第五阶段：检查完成判断 -->
        <mxCell id="phase5-title" value="完成判断阶段" style="text;html=1;strokeColor=none;fillColor=#fff2cc;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="730" y="80" width="120" height="30" as="geometry" />
        </mxCell>

        <mxCell id="all-complete" value="所有模块\n完成?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="750" y="130" width="80" height="60" as="geometry" />
        </mxCell>

        <!-- 第六阶段：报告生成 -->
        <mxCell id="phase6-title" value="报告生成阶段" style="text;html=1;strokeColor=none;fillColor=#e1d5e7;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="900" y="80" width="120" height="30" as="geometry" />
        </mxCell>

        <mxCell id="summary" value="检查结果汇总" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="900" y="130" width="120" height="40" as="geometry" />
        </mxCell>

        <mxCell id="report" value="生成检查报告" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="900" y="190" width="120" height="40" as="geometry" />
        </mxCell>

        <mxCell id="audit" value="报告审核导出" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="900" y="250" width="120" height="40" as="geometry" />
        </mxCell>

        <mxCell id="archive" value="数据存档" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="900" y="310" width="120" height="40" as="geometry" />
        </mxCell>

        <!-- 结束节点 -->
        <mxCell id="end" value="任务结束" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="1060" y="220" width="100" height="50" as="geometry" />
        </mxCell>

        <!-- 单项检查详细流程 -->
        <mxCell id="single-flow-title" value="单项检查详细流程" style="text;html=1;strokeColor=none;fillColor=#f0f0f0;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="50" y="580" width="650" height="30" as="geometry" />
        </mxCell>

        <mxCell id="single-start" value="进入检查项" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="50" y="630" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="show-title" value="显示检查标题" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="150" y="630" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="safety-critical" value="安全关键项?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="250" y="620" width="60" height="60" as="geometry" />
        </mxCell>

        <mxCell id="safety-warning" value="安全警告" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="250" y="700" width="60" height="30" as="geometry" />
        </mxCell>

        <mxCell id="execute-check" value="执行检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="330" y="630" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="need-data" value="需要数据?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="430" y="620" width="60" height="60" as="geometry" />
        </mxCell>

        <mxCell id="data-input" value="数据输入" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="430" y="700" width="60" height="30" as="geometry" />
        </mxCell>

        <mxCell id="need-photo" value="需要拍照?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="510" y="620" width="60" height="60" as="geometry" />
        </mxCell>

        <mxCell id="take-photo" value="拍照功能" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="510" y="700" width="60" height="30" as="geometry" />
        </mxCell>

        <mxCell id="result-confirm" value="结果确认" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="590" y="630" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="pass-fail" value="合格?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="690" y="620" width="60" height="60" as="geometry" />
        </mxCell>

        <mxCell id="mark-pass" value="标记完成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="770" y="600" width="60" height="30" as="geometry" />
        </mxCell>

        <mxCell id="mark-fail" value="问题描述" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="770" y="650" width="60" height="30" as="geometry" />
        </mxCell>

        <mxCell id="update-progress" value="更新进度" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="850" y="630" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="next-item" value="下一项" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10" vertex="1" parent="1">
          <mxGeometry x="950" y="630" width="60" height="40" as="geometry" />
        </mxCell>

        <!-- 主流程连线 -->
        <!-- 系统准备阶段连线 -->
        <mxCell id="edge1" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="start" target="login">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="110" y="180" as="sourcePoint" />
            <mxPoint x="110" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge2" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="login" target="init">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="110" y="240" as="sourcePoint" />
            <mxPoint x="110" y="260" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 任务配置阶段连线 -->
        <mxCell id="edge3" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="init" target="task-create">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="170" y="280" as="sourcePoint" />
            <mxPoint x="220" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge4" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="task-create" target="team-config">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="280" y="170" as="sourcePoint" />
            <mxPoint x="280" y="190" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge5" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="team-config" target="vehicle-info">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="280" y="230" as="sourcePoint" />
            <mxPoint x="280" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 设备检查阶段连线 -->
        <mxCell id="edge6" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="vehicle-info" target="device-check">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="340" y="270" as="sourcePoint" />
            <mxPoint x="390" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge7" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="device-check" target="device-ok">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="450" y="170" as="sourcePoint" />
            <mxPoint x="450" y="190" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge8" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="device-ok" target="device-error">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="450" y="250" as="sourcePoint" />
            <mxPoint x="450" y="270" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge9" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="device-ok" target="check-main">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="490" y="220" as="sourcePoint" />
            <mxPoint x="560" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge10" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="device-error" target="device-check">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="290" as="sourcePoint" />
            <mxPoint x="390" y="170" as="targetPoint" />
            <Array as="points">
              <mxPoint x="350" y="290" />
              <mxPoint x="350" y="150" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- 检查模块连线 -->
        <mxCell id="edge11" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="check-main" target="safety-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="620" y="170" as="sourcePoint" />
            <mxPoint x="100" y="400" as="targetPoint" />
            <Array as="points">
              <mxPoint x="620" y="320" />
              <mxPoint x="100" y="320" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="edge12" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="check-main" target="appearance-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="620" y="170" as="sourcePoint" />
            <mxPoint x="210" y="400" as="targetPoint" />
            <Array as="points">
              <mxPoint x="620" y="320" />
              <mxPoint x="210" y="320" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="edge13" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="check-main" target="electrical-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="620" y="170" as="sourcePoint" />
            <mxPoint x="320" y="400" as="targetPoint" />
            <Array as="points">
              <mxPoint x="620" y="320" />
              <mxPoint x="320" y="320" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="edge14" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="check-main" target="sensor-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="620" y="170" as="sourcePoint" />
            <mxPoint x="430" y="400" as="targetPoint" />
            <Array as="points">
              <mxPoint x="620" y="320" />
              <mxPoint x="430" y="320" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="edge15" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="check-main" target="tool-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="620" y="170" as="sourcePoint" />
            <mxPoint x="540" y="400" as="targetPoint" />
            <Array as="points">
              <mxPoint x="620" y="320" />
              <mxPoint x="540" y="320" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="edge16" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="check-main" target="system-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="620" y="170" as="sourcePoint" />
            <mxPoint x="650" y="400" as="targetPoint" />
            <Array as="points">
              <mxPoint x="620" y="320" />
              <mxPoint x="650" y="320" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- 完成判断和报告生成连线 -->
        <mxCell id="edge17" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="check-main" target="all-complete">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="680" y="150" as="sourcePoint" />
            <mxPoint x="750" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge18" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="all-complete" target="summary">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="830" y="160" as="sourcePoint" />
            <mxPoint x="900" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge19" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="summary" target="report">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="960" y="170" as="sourcePoint" />
            <mxPoint x="960" y="190" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge20" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="report" target="audit">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="960" y="230" as="sourcePoint" />
            <mxPoint x="960" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge21" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="audit" target="archive">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="960" y="290" as="sourcePoint" />
            <mxPoint x="960" y="310" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge22" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="archive" target="end">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1020" y="330" as="sourcePoint" />
            <mxPoint x="1060" y="245" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 循环检查连线 -->
        <mxCell id="edge23" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="all-complete" target="check-main">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="750" y="160" as="sourcePoint" />
            <mxPoint x="620" y="170" as="targetPoint" />
            <Array as="points">
              <mxPoint x="720" y="160" />
              <mxPoint x="720" y="200" />
              <mxPoint x="620" y="200" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- 单项检查流程连线 -->
        <mxCell id="edge24" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="single-start" target="show-title">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="130" y="650" as="sourcePoint" />
            <mxPoint x="150" y="650" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge25" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="show-title" target="safety-critical">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="230" y="650" as="sourcePoint" />
            <mxPoint x="250" y="650" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge26" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="safety-critical" target="safety-warning">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="280" y="680" as="sourcePoint" />
            <mxPoint x="280" y="700" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge27" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="safety-critical" target="execute-check">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="310" y="650" as="sourcePoint" />
            <mxPoint x="330" y="650" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge28" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="safety-warning" target="execute-check">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="310" y="715" as="sourcePoint" />
            <mxPoint x="370" y="670" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge29" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="execute-check" target="need-data">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="410" y="650" as="sourcePoint" />
            <mxPoint x="430" y="650" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge30" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="need-data" target="data-input">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="460" y="680" as="sourcePoint" />
            <mxPoint x="460" y="700" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge31" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="need-data" target="need-photo">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="490" y="650" as="sourcePoint" />
            <mxPoint x="510" y="650" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge32" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="data-input" target="need-photo">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="490" y="715" as="sourcePoint" />
            <mxPoint x="540" y="680" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge33" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="need-photo" target="take-photo">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="540" y="680" as="sourcePoint" />
            <mxPoint x="540" y="700" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge34" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="need-photo" target="result-confirm">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="570" y="650" as="sourcePoint" />
            <mxPoint x="590" y="650" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge35" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="take-photo" target="result-confirm">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="570" y="715" as="sourcePoint" />
            <mxPoint x="630" y="670" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge36" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="result-confirm" target="pass-fail">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="670" y="650" as="sourcePoint" />
            <mxPoint x="690" y="650" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge37" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="pass-fail" target="mark-pass">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="720" y="620" as="sourcePoint" />
            <mxPoint x="770" y="615" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge38" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="pass-fail" target="mark-fail">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="720" y="660" as="sourcePoint" />
            <mxPoint x="770" y="665" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge39" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="mark-pass" target="update-progress">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="830" y="615" as="sourcePoint" />
            <mxPoint x="850" y="650" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge40" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="mark-fail" target="update-progress">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="830" y="665" as="sourcePoint" />
            <mxPoint x="850" y="650" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge41" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="update-progress" target="next-item">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="930" y="650" as="sourcePoint" />
            <mxPoint x="950" y="650" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 标签文字 -->
        <mxCell id="label1" value="通过" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="520" y="200" width="30" height="15" as="geometry" />
        </mxCell>

        <mxCell id="label2" value="异常" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="450" y="250" width="30" height="15" as="geometry" />
        </mxCell>

        <mxCell id="label3" value="完成" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="850" y="140" width="30" height="15" as="geometry" />
        </mxCell>

        <mxCell id="label4" value="继续" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="680" y="180" width="30" height="15" as="geometry" />
        </mxCell>

        <mxCell id="label5" value="是" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="280" y="680" width="15" height="15" as="geometry" />
        </mxCell>

        <mxCell id="label6" value="否" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="320" y="640" width="15" height="15" as="geometry" />
        </mxCell>

        <mxCell id="label7" value="是" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="460" y="680" width="15" height="15" as="geometry" />
        </mxCell>

        <mxCell id="label8" value="否" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="500" y="640" width="15" height="15" as="geometry" />
        </mxCell>

        <mxCell id="label9" value="是" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="540" y="680" width="15" height="15" as="geometry" />
        </mxCell>

        <mxCell id="label10" value="否" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="580" y="640" width="15" height="15" as="geometry" />
        </mxCell>

        <mxCell id="label11" value="合格" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="730" y="590" width="25" height="15" as="geometry" />
        </mxCell>

        <mxCell id="label12" value="不合格" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="730" y="680" width="35" height="15" as="geometry" />
        </mxCell>

        <!-- 流程说明 -->
        <mxCell id="description" value="流程说明：&#xa;1. 系统按阶段顺序执行&#xa;2. 设备检查异常时需重新检查&#xa;3. 六大模块可并行执行&#xa;4. 单项检查支持数据录入和拍照&#xa;5. 所有模块完成后生成报告" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=10;align=left;verticalAlign=top" vertex="1" parent="1">
          <mxGeometry x="50" y="760" width="250" height="100" as="geometry" />
        </mxCell>

        <!-- 图例 -->
        <mxCell id="legend-title" value="图例说明" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="750" y="760" width="80" height="20" as="geometry" />
        </mxCell>

        <mxCell id="legend1" value="系统准备" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="750" y="790" width="60" height="20" as="geometry" />
        </mxCell>

        <mxCell id="legend2" value="任务配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="820" y="790" width="60" height="20" as="geometry" />
        </mxCell>

        <mxCell id="legend3" value="设备检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="890" y="790" width="60" height="20" as="geometry" />
        </mxCell>

        <mxCell id="legend4" value="检查执行" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="960" y="790" width="60" height="20" as="geometry" />
        </mxCell>

        <mxCell id="legend5" value="报告生成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="750" y="820" width="60" height="20" as="geometry" />
        </mxCell>

        <mxCell id="legend6" value="异常处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=9" vertex="1" parent="1">
          <mxGeometry x="820" y="820" width="60" height="20" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
